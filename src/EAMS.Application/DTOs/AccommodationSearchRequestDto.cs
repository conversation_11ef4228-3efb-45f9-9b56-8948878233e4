using System.ComponentModel;
using EAMS.Application.Converters;

namespace EAMS.Application.DTOs;

public class AccommodationSearchRequestDto : SearchRequestDto
{
    /// <summary>
    /// Search term to match against accommodation name and address fields
    /// </summary>
    public new string? SearchTerm { get; set; }
    
    /// <summary>
    /// Latitude for distance-based filtering (must be provided with Longitude and Radius)
    /// </summary>
    public double? Latitude { get; set; }
    
    /// <summary>
    /// Longitude for distance-based filtering (must be provided with Latitude and Radius)
    /// </summary>
    public double? Longitude { get; set; }
    
    /// <summary>
    /// Radius in kilometers for distance-based filtering (must be provided with Latitude and Longitude)
    /// </summary>
    public double? Radius { get; set; }

    /// <summary>
    /// Optional accommodation type filter (multiple) - supports comma-separated values
    /// </summary>
    [TypeConverter(typeof(CommaDelimitedListConverter))]
    public List<int>? AccommodationTypeIds { get; set; }

    /// <summary>
    /// Optional amenity filter (multiple) - supports comma-separated values
    /// </summary>
    [TypeConverter(typeof(CommaDelimitedListConverter))]
    public List<int>? AmenityIds { get; set; }

    public override bool ValidateQuery()
    {
        // First validate base pagination requirements
        var baseValidation = base.ValidateQuery();
        if (!baseValidation)
            return false;

        // Validate distance filtering parameters - all three must be provided together or none at all
        var hasLatitude = Latitude.HasValue;
        var hasLongitude = Longitude.HasValue;
        var hasRadius = Radius.HasValue;

        if (hasLatitude || hasLongitude || hasRadius)
        {
            // If any distance parameter is provided, all must be provided
            if (!hasLatitude || !hasLongitude || !hasRadius)
                return false;

            // Validate latitude range
            if (Latitude < -90 || Latitude > 90)
                return false;

            // Validate longitude range
            if (Longitude < -180 || Longitude > 180)
                return false;

            // Validate radius is positive
            if (Radius <= 0)
                return false;
        }

        // Set default sorting if not provided
        if (Sorting is null)
        {
            Sorting = new SearchSorting
            {
                OrderBy = "name",
                Direction = "asc"
            };
        }

        if (AccommodationTypeIds?.Any() == true && AccommodationTypeIds.Any(id => id <= 0))
            return false;

        if (AmenityIds?.Any() == true && AmenityIds.Any(id => id <= 0))
            return false;

        return true;
    }
}
